//+------------------------------------------------------------------+
//|                                           TrendBreakout_EA.mq4 |
//|                                                                  |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright ""
#property link      ""
#property version   "1.00"
#property strict

// 输入参数
extern int    LookbackBars = 5;        // 前N根K线数量
extern double BreakoutPoints = 500;    // 突破点数
extern int    StopLossBars = 7;        // 止损参考K线数量
extern double StopLossPoints = 50;     // 止损额外点数
extern int    CloseBars = 3;           // 平仓参考K线数量
extern double TrailingProfitPoints = 400; // 移动止损盈利点数
extern double TrailingStopPoints = 44;    // 移动止损点数
extern bool   UseMACDFilter = false;   // 启用MACD过滤
extern int    MACDFastEMA = 12;        // MACD快线EMA周期
extern int    MACDSlowEMA = 26;        // MACD慢线EMA周期
extern int    MACDSignalSMA = 9;       // MACD信号线SMA周期
extern bool   UseRecoveryMode = false; // 启用止损触发回本功能
extern bool   UseRecoveryTP = true;    // 启用回本单止盈
extern double LotSize = 0.1;           // 手数
extern int    MagicNumber = 12345;     // 魔术数字基数
extern int    Slippage = 3;            // 滑点

// 全局变量
datetime lastBarTime = 0;
double originalStopLoss = 0;           // 首单原始止损价格
double originalOpenPrice = 0;          // 首单开仓价格
double originalLotSize = 0;            // 首单手数
int originalOrderType = -1;            // 首单类型
bool isRecoveryOrder = false;          // 是否为回本单
bool waitingForRecovery = false;       // 等待回本单开仓
int uniqueMagicNumber = 0;             // 当前图表的唯一魔术数字
int recoveryMagicNumber = 0;           // 回本单魔术数字

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // 为每个图表生成唯一的魔术数字
   // 使用货币对名称和时间周期的哈希值来确保唯一性
   string symbolPeriod = Symbol() + IntegerToString(Period());
   int hashValue = 0;
   for(int i = 0; i < StringLen(symbolPeriod) && i < 4; i++)
   {
      hashValue += StringGetCharacter(symbolPeriod, i);
   }
   uniqueMagicNumber = MagicNumber + hashValue;
   recoveryMagicNumber = uniqueMagicNumber + 10000;

   Print("EA初始化 - 货币对: ", Symbol(), ", 周期: ", Period(),
         ", 唯一魔术数字: ", uniqueMagicNumber, ", 回本单魔术数字: ", recoveryMagicNumber);

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // 检查首单是否已经不存在，如果不存在则重置回本单状态
   if(originalOrderType != -1 && !isRecoveryOrder && CountOrders() == 0)
   {
      Print("检测到首单已平仓，重置回本单状态");
      ResetRecoveryState();
   }

   // 检查回本单触发
   if(UseRecoveryMode && !isRecoveryOrder)
      CheckRecoveryTrigger();

   // 检查是否有新K线
   if(Time[0] == lastBarTime)
      return;
   lastBarTime = Time[0];

   // 移动止损（仅对首单，不对回本单）
   if(TrailingProfitPoints > 0 && !isRecoveryOrder)
      TrailingStop();

   // 检查平仓条件
   CheckCloseConditions();

   // 检查开仓条件
   CheckOpenConditions();
}

//+------------------------------------------------------------------+
//| 检查开仓条件                                                      |
//+------------------------------------------------------------------+
void CheckOpenConditions()
{
   // 限制持仓单数为一单：如果已有任何持仓，不再开新仓
   if(CountAllOrders() > 0)
      return;

   // 如果等待回本单，跳过正常开仓逻辑
   if(waitingForRecovery)
      return;

   // 检查做空条件
   if(CheckShortCondition())
   {
      double stopLoss = GetHighestPrice(StopLossBars, 2) + StopLossPoints * Point;
      int ticket = OrderSend(Symbol(), OP_SELL, LotSize, Bid, Slippage, stopLoss, 0,
                            "TrendBreakout Short", uniqueMagicNumber, 0, Red);
      if(ticket > 0)
      {
         Print("空单开仓成功，票号: ", ticket);
         // 记录首单信息
         originalStopLoss = stopLoss;
         originalOpenPrice = Bid;
         originalLotSize = LotSize;
         originalOrderType = OP_SELL;
         isRecoveryOrder = false;
      }
   }

   // 检查做多条件
   if(CheckLongCondition())
   {
      double stopLoss = GetLowestPrice(StopLossBars, 2) - StopLossPoints * Point;
      int ticket = OrderSend(Symbol(), OP_BUY, LotSize, Ask, Slippage, stopLoss, 0,
                            "TrendBreakout Long", uniqueMagicNumber, 0, Blue);
      if(ticket > 0)
      {
         Print("多单开仓成功，票号: ", ticket);
         // 记录首单信息
         originalStopLoss = stopLoss;
         originalOpenPrice = Ask;
         originalLotSize = LotSize;
         originalOrderType = OP_BUY;
         isRecoveryOrder = false;
      }
   }
}

//+------------------------------------------------------------------+
//| 检查做空条件                                                      |
//+------------------------------------------------------------------+
bool CheckShortCondition()
{
   // 1. 前5根K线到上上上柱K线最低点依次增加
   for(int i = LookbackBars; i >= 4; i--)
   {
      if(Low[i] >= Low[i-1])
         return false;
   }

   // 2. 前4根K线（包括前第四根k线）到上上柱（包括上上根k线）之间至少有一根阴线
   bool hasRedCandle = false;
   for(int i = LookbackBars; i >= 2; i--)
   {
      if(Close[i] < Open[i])
      {
         hasRedCandle = true;
         break;
      }
   }
   if(!hasRedCandle)
      return false;

   // 3. 上上柱开盘价比第5根最低点高500*point
   double fifthBarLow = Low[LookbackBars];
   if(Open[2] <= fifthBarLow + BreakoutPoints * Point)
      return false;
   
   // 4. 上柱为阴线
   if(Close[1] >= Open[1])
      return false;
   
   // 5. 上柱最低点小于上上柱最低点
   if(Low[1] >= Low[2])
      return false;
   
   // 6. MACD过滤条件
   if(UseMACDFilter)
   {
      double macdMain = iMACD(Symbol(), 0, MACDFastEMA, MACDSlowEMA, MACDSignalSMA, PRICE_CLOSE, MODE_MAIN, 1);
      if(macdMain >= 0)
         return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 检查做多条件                                                      |
//+------------------------------------------------------------------+
bool CheckLongCondition()
{
   // 1. 前5根K线到上上上柱K线最高点依次降低
   for(int i = LookbackBars; i >= 4; i--)
   {
      if(High[i] <= High[i-1])
         return false;
   }

   // 2. 前4根K线（包括前第四根k线）到上上柱（包括上上根k线）之间至少有一根阳线
   bool hasGreenCandle = false;
   for(int i = LookbackBars; i >= 2; i--)
   {
      if(Close[i] > Open[i])
      {
         hasGreenCandle = true;
         break;
      }
   }
   if(!hasGreenCandle)
      return false;

   // 3. 上上柱开盘价比第5根最高点低500*point
   double fifthBarHigh = High[LookbackBars];
   if(Open[2] >= fifthBarHigh - BreakoutPoints * Point)
      return false;
   
   // 4. 上柱为阳线
   if(Close[1] <= Open[1])
      return false;
   
   // 5. 上柱最高点大于上上柱最高点
   if(High[1] <= High[2])
      return false;
   
   // 6. MACD过滤条件
   if(UseMACDFilter)
   {
      double macdMain = iMACD(Symbol(), 0, MACDFastEMA, MACDSlowEMA, MACDSignalSMA, PRICE_CLOSE, MODE_MAIN, 1);
      if(macdMain <= 0)
         return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| 检查平仓条件                                                      |
//+------------------------------------------------------------------+
void CheckCloseConditions()
{
   // 平多单条件：前3根K线最高点依次降低
   bool closeLong = true;
   for(int i = CloseBars; i >= 2; i--)
   {
      if(High[i] <= High[i-1])
      {
         closeLong = false;
         break;
      }
   }

   // 平空单条件：前3根K线最低点依次增加
   bool closeShort = true;
   for(int i = CloseBars; i >= 2; i--)
   {
      if(Low[i] >= Low[i-1])
      {
         closeShort = false;
         break;
      }
   }

   bool hasClosedOrder = false;

   // 执行平仓
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS) &&
         (OrderMagicNumber() == uniqueMagicNumber || OrderMagicNumber() == recoveryMagicNumber) &&
         OrderSymbol() == Symbol())
      {
         if(OrderType() == OP_BUY && closeLong)
         {
            bool result = OrderClose(OrderTicket(), OrderLots(), Bid, Slippage, Green);
            if(result)
            {
               Print("多单平仓成功，票号: ", OrderTicket());
               hasClosedOrder = true;
            }
         }
         else if(OrderType() == OP_SELL && closeShort)
         {
            bool result = OrderClose(OrderTicket(), OrderLots(), Ask, Slippage, Red);
            if(result)
            {
               Print("空单平仓成功，票号: ", OrderTicket());
               hasClosedOrder = true;
            }
         }
      }
   }

   // 如果有订单被平仓，检查是否需要重置回本单状态
   if(hasClosedOrder && CountAllOrders() == 0)
   {
      ResetRecoveryState();
   }
}

//+------------------------------------------------------------------+
//| 移动止损                                                          |
//+------------------------------------------------------------------+
void TrailingStop()
{
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderMagicNumber() == uniqueMagicNumber && OrderSymbol() == Symbol())
      {
         if(OrderType() == OP_BUY)
         {
            // 多单：检测上一柱最低点是否盈利400*point
            double profit = (Low[1] - OrderOpenPrice()) / Point;
            if(profit >= TrailingProfitPoints)
            {
               double newStopLoss = Low[1] - TrailingStopPoints * Point;
               if(newStopLoss > OrderStopLoss())
               {
                  bool result = OrderModify(OrderTicket(), OrderOpenPrice(), newStopLoss, OrderTakeProfit(), 0, Blue);
                  if(result)
                     Print("多单移动止损成功，新止损: ", newStopLoss);
               }
            }
         }
         else if(OrderType() == OP_SELL)
         {
            // 空单：检测上一柱最高点是否盈利400*point
            double profit = (OrderOpenPrice() - High[1]) / Point;
            if(profit >= TrailingProfitPoints)
            {
               double newStopLoss = High[1] + TrailingStopPoints * Point;
               if(newStopLoss < OrderStopLoss() || OrderStopLoss() == 0)
               {
                  bool result = OrderModify(OrderTicket(), OrderOpenPrice(), newStopLoss, OrderTakeProfit(), 0, Red);
                  if(result)
                     Print("空单移动止损成功，新止损: ", newStopLoss);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 获取指定范围内的最高价                                            |
//+------------------------------------------------------------------+
double GetHighestPrice(int bars, int startBar)
{
   double highest = High[startBar];
   for(int i = startBar; i < startBar + bars; i++)
   {
      if(High[i] > highest)
         highest = High[i];
   }
   return highest;
}

//+------------------------------------------------------------------+
//| 获取指定范围内的最低价                                            |
//+------------------------------------------------------------------+
double GetLowestPrice(int bars, int startBar)
{
   double lowest = Low[startBar];
   for(int i = startBar; i < startBar + bars; i++)
   {
      if(Low[i] < lowest)
         lowest = Low[i];
   }
   return lowest;
}

//+------------------------------------------------------------------+
//| 统计当前持仓数量                                                  |
//+------------------------------------------------------------------+
int CountOrders()
{
   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderMagicNumber() == uniqueMagicNumber && OrderSymbol() == Symbol())
         count++;
   }
   return count;
}

//+------------------------------------------------------------------+
//| 统计所有订单数量（包括回本单）                                    |
//+------------------------------------------------------------------+
int CountAllOrders()
{
   int count = 0;
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS) &&
         (OrderMagicNumber() == uniqueMagicNumber || OrderMagicNumber() == recoveryMagicNumber) &&
         OrderSymbol() == Symbol())
         count++;
   }
   return count;
}

//+------------------------------------------------------------------+
//| 检查回本单触发条件                                                |
//+------------------------------------------------------------------+
void CheckRecoveryTrigger()
{
   if(originalOrderType == -1 || waitingForRecovery)
      return;

   // 检查是否触发原始止损
   bool triggerRecovery = false;
   double currentPrice = 0;

   if(originalOrderType == OP_BUY)
   {
      currentPrice = Bid;
      if(currentPrice <= originalStopLoss)
         triggerRecovery = true;
   }
   else if(originalOrderType == OP_SELL)
   {
      currentPrice = Ask;
      if(currentPrice >= originalStopLoss)
         triggerRecovery = true;
   }

   if(triggerRecovery)
   {
      Print("触发回本单条件，当前价格: ", currentPrice, ", 原始止损: ", originalStopLoss);

      // 先平掉首单，确保持仓单数限制
      CloseOriginalOrder();

      waitingForRecovery = true;

      // 立即开回本单
      OpenRecoveryOrder(currentPrice);
   }
}

//+------------------------------------------------------------------+
//| 开启回本单                                                        |
//+------------------------------------------------------------------+
void OpenRecoveryOrder(double triggerPrice)
{
   double stopLoss = 0;
   double takeProfit = 0;
   double lossPoints = MathAbs(originalOpenPrice - originalStopLoss) / Point;

   if(originalOrderType == OP_BUY)
   {
      // 原单为多单，开空单回本
      stopLoss = GetHighestPrice(CloseBars, 1); // 前三柱最高点
      if(UseRecoveryTP)
         takeProfit = triggerPrice - lossPoints * Point;

      int ticket = OrderSend(Symbol(), OP_SELL, originalLotSize, Bid, Slippage, stopLoss, takeProfit,
                            "Recovery Short", recoveryMagicNumber, 0, Orange);
      if(ticket > 0)
      {
         Print("回本空单开仓成功，票号: ", ticket, ", 止损: ", stopLoss, ", 止盈: ", takeProfit);
         isRecoveryOrder = true;
         waitingForRecovery = false;
      }
   }
   else if(originalOrderType == OP_SELL)
   {
      // 原单为空单，开多单回本
      stopLoss = GetLowestPrice(CloseBars, 1); // 前三柱最低点
      if(UseRecoveryTP)
         takeProfit = triggerPrice + lossPoints * Point;

      int ticket = OrderSend(Symbol(), OP_BUY, originalLotSize, Ask, Slippage, stopLoss, takeProfit,
                            "Recovery Long", recoveryMagicNumber, 0, Orange);
      if(ticket > 0)
      {
         Print("回本多单开仓成功，票号: ", ticket, ", 止损: ", stopLoss, ", 止盈: ", takeProfit);
         isRecoveryOrder = true;
         waitingForRecovery = false;
      }
   }
}

//+------------------------------------------------------------------+
//| 重置回本单状态                                                    |
//+------------------------------------------------------------------+
void ResetRecoveryState()
{
   originalStopLoss = 0;
   originalOpenPrice = 0;
   originalLotSize = 0;
   originalOrderType = -1;
   isRecoveryOrder = false;
   waitingForRecovery = false;
   Print("回本单状态已重置");
}

//+------------------------------------------------------------------+
//| 平掉首单                                                          |
//+------------------------------------------------------------------+
void CloseOriginalOrder()
{
   for(int i = OrdersTotal() - 1; i >= 0; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS) && OrderMagicNumber() == uniqueMagicNumber && OrderSymbol() == Symbol())
      {
         bool result = false;
         if(OrderType() == OP_BUY)
         {
            result = OrderClose(OrderTicket(), OrderLots(), Bid, Slippage, Green);
         }
         else if(OrderType() == OP_SELL)
         {
            result = OrderClose(OrderTicket(), OrderLots(), Ask, Slippage, Red);
         }

         if(result)
         {
            Print("首单平仓成功，票号: ", OrderTicket(), ", 触发回本单逻辑");
         }
         else
         {
            Print("首单平仓失败，票号: ", OrderTicket(), ", 错误: ", GetLastError());
         }
         break; // 只平掉一个首单
      }
   }
}


